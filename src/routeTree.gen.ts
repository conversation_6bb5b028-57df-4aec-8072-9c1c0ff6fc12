/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as DashboardImport } from './routes/_dashboard'
import { Route as AuthenticationImport } from './routes/_authentication'
import { Route as IndexImport } from './routes/index'
import { Route as Dashboard403Import } from './routes/_dashboard/403'
import { Route as FlowEditorIndexImport } from './routes/flow_/editor/index'
import { Route as DashboardFaultIndexImport } from './routes/_dashboard/fault/index'
import { Route as DashboardDashboardIndexImport } from './routes/_dashboard/dashboard/index'
import { Route as AuthenticationSignUpIndexImport } from './routes/_authentication/sign-up/index'
import { Route as AuthenticationSignInIndexImport } from './routes/_authentication/sign-in/index'
import { Route as DashboardSystemConfigRouteImport } from './routes/_dashboard/system/config/route'
import { Route as DashboardUserMyApplicationIndexImport } from './routes/_dashboard/user/my-application/index'
import { Route as DashboardTraceSessionIndexImport } from './routes/_dashboard/trace/session/index'
import { Route as DashboardTraceIssueIndexImport } from './routes/_dashboard/trace/issue/index'
import { Route as DashboardSystemUserIndexImport } from './routes/_dashboard/system/user/index'
import { Route as DashboardSystemRoleIndexImport } from './routes/_dashboard/system/role/index'
import { Route as DashboardSystemPermissionIndexImport } from './routes/_dashboard/system/permission/index'
import { Route as DashboardSystemJobIndexImport } from './routes/_dashboard/system/job/index'
import { Route as DashboardSystemApiIndexImport } from './routes/_dashboard/system/api/index'
import { Route as DashboardPaymentSubscriptionIndexImport } from './routes/_dashboard/payment/subscription/index'
import { Route as DashboardPaymentRechargeIndexImport } from './routes/_dashboard/payment/recharge/index'
import { Route as DashboardExportBigDataIndexImport } from './routes/_dashboard/export/big-data/index'
import { Route as DashboardComponentS3UploadIndexImport } from './routes/_dashboard/component/s3-upload/index'
import { Route as DashboardComponentRichTextEditorIndexImport } from './routes/_dashboard/component/rich-text-editor/index'
import { Route as DashboardTraceIssueIdImport } from './routes/_dashboard/trace/issue/$id'
import { Route as DashboardSystemRoleIdImport } from './routes/_dashboard/system/role/$id'
import { Route as DashboardSystemConfigIdImport } from './routes/_dashboard/system/config/$id'
import { Route as AuthenticationOauth2CodeFeishuImport } from './routes/_authentication/oauth2/code/feishu'
import { Route as DashboardECommerceProductCategoryIndexImport } from './routes/_dashboard/e-commerce/product/category/index'

// Create/Update Routes

const DashboardRoute = DashboardImport.update({
  id: '/_dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticationRoute = AuthenticationImport.update({
  id: '/_authentication',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const Dashboard403Route = Dashboard403Import.update({
  id: '/403',
  path: '/403',
  getParentRoute: () => DashboardRoute,
} as any)

const FlowEditorIndexRoute = FlowEditorIndexImport.update({
  id: '/flow_/editor/',
  path: '/flow/editor/',
  getParentRoute: () => rootRoute,
} as any)

const DashboardFaultIndexRoute = DashboardFaultIndexImport.update({
  id: '/fault/',
  path: '/fault/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardDashboardIndexRoute = DashboardDashboardIndexImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => DashboardRoute,
} as any)

const AuthenticationSignUpIndexRoute = AuthenticationSignUpIndexImport.update({
  id: '/sign-up/',
  path: '/sign-up/',
  getParentRoute: () => AuthenticationRoute,
} as any)

const AuthenticationSignInIndexRoute = AuthenticationSignInIndexImport.update({
  id: '/sign-in/',
  path: '/sign-in/',
  getParentRoute: () => AuthenticationRoute,
} as any)

const DashboardSystemConfigRouteRoute = DashboardSystemConfigRouteImport.update(
  {
    id: '/system/config',
    path: '/system/config',
    getParentRoute: () => DashboardRoute,
  } as any,
)

const DashboardUserMyApplicationIndexRoute =
  DashboardUserMyApplicationIndexImport.update({
    id: '/user/my-application/',
    path: '/user/my-application/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardTraceSessionIndexRoute = DashboardTraceSessionIndexImport.update(
  {
    id: '/trace/session/',
    path: '/trace/session/',
    getParentRoute: () => DashboardRoute,
  } as any,
)

const DashboardTraceIssueIndexRoute = DashboardTraceIssueIndexImport.update({
  id: '/trace/issue/',
  path: '/trace/issue/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemUserIndexRoute = DashboardSystemUserIndexImport.update({
  id: '/system/user/',
  path: '/system/user/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemRoleIndexRoute = DashboardSystemRoleIndexImport.update({
  id: '/system/role/',
  path: '/system/role/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemPermissionIndexRoute =
  DashboardSystemPermissionIndexImport.update({
    id: '/system/permission/',
    path: '/system/permission/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardSystemJobIndexRoute = DashboardSystemJobIndexImport.update({
  id: '/system/job/',
  path: '/system/job/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemApiIndexRoute = DashboardSystemApiIndexImport.update({
  id: '/system/api/',
  path: '/system/api/',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardPaymentSubscriptionIndexRoute =
  DashboardPaymentSubscriptionIndexImport.update({
    id: '/payment/subscription/',
    path: '/payment/subscription/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardPaymentRechargeIndexRoute =
  DashboardPaymentRechargeIndexImport.update({
    id: '/payment/recharge/',
    path: '/payment/recharge/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardExportBigDataIndexRoute =
  DashboardExportBigDataIndexImport.update({
    id: '/export/big-data/',
    path: '/export/big-data/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardComponentS3UploadIndexRoute =
  DashboardComponentS3UploadIndexImport.update({
    id: '/component/s3-upload/',
    path: '/component/s3-upload/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardComponentRichTextEditorIndexRoute =
  DashboardComponentRichTextEditorIndexImport.update({
    id: '/component/rich-text-editor/',
    path: '/component/rich-text-editor/',
    getParentRoute: () => DashboardRoute,
  } as any)

const DashboardTraceIssueIdRoute = DashboardTraceIssueIdImport.update({
  id: '/trace/issue/$id',
  path: '/trace/issue/$id',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemRoleIdRoute = DashboardSystemRoleIdImport.update({
  id: '/system/role/$id',
  path: '/system/role/$id',
  getParentRoute: () => DashboardRoute,
} as any)

const DashboardSystemConfigIdRoute = DashboardSystemConfigIdImport.update({
  id: '/$id',
  path: '/$id',
  getParentRoute: () => DashboardSystemConfigRouteRoute,
} as any)

const AuthenticationOauth2CodeFeishuRoute =
  AuthenticationOauth2CodeFeishuImport.update({
    id: '/oauth2/code/feishu',
    path: '/oauth2/code/feishu',
    getParentRoute: () => AuthenticationRoute,
  } as any)

const DashboardECommerceProductCategoryIndexRoute =
  DashboardECommerceProductCategoryIndexImport.update({
    id: '/e-commerce/product/category/',
    path: '/e-commerce/product/category/',
    getParentRoute: () => DashboardRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_authentication': {
      id: '/_authentication'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticationImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard': {
      id: '/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/_dashboard/403': {
      id: '/_dashboard/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof Dashboard403Import
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/config': {
      id: '/_dashboard/system/config'
      path: '/system/config'
      fullPath: '/system/config'
      preLoaderRoute: typeof DashboardSystemConfigRouteImport
      parentRoute: typeof DashboardImport
    }
    '/_authentication/sign-in/': {
      id: '/_authentication/sign-in/'
      path: '/sign-in'
      fullPath: '/sign-in'
      preLoaderRoute: typeof AuthenticationSignInIndexImport
      parentRoute: typeof AuthenticationImport
    }
    '/_authentication/sign-up/': {
      id: '/_authentication/sign-up/'
      path: '/sign-up'
      fullPath: '/sign-up'
      preLoaderRoute: typeof AuthenticationSignUpIndexImport
      parentRoute: typeof AuthenticationImport
    }
    '/_dashboard/dashboard/': {
      id: '/_dashboard/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardDashboardIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/fault/': {
      id: '/_dashboard/fault/'
      path: '/fault'
      fullPath: '/fault'
      preLoaderRoute: typeof DashboardFaultIndexImport
      parentRoute: typeof DashboardImport
    }
    '/flow_/editor/': {
      id: '/flow_/editor/'
      path: '/flow/editor'
      fullPath: '/flow/editor'
      preLoaderRoute: typeof FlowEditorIndexImport
      parentRoute: typeof rootRoute
    }
    '/_authentication/oauth2/code/feishu': {
      id: '/_authentication/oauth2/code/feishu'
      path: '/oauth2/code/feishu'
      fullPath: '/oauth2/code/feishu'
      preLoaderRoute: typeof AuthenticationOauth2CodeFeishuImport
      parentRoute: typeof AuthenticationImport
    }
    '/_dashboard/system/config/$id': {
      id: '/_dashboard/system/config/$id'
      path: '/$id'
      fullPath: '/system/config/$id'
      preLoaderRoute: typeof DashboardSystemConfigIdImport
      parentRoute: typeof DashboardSystemConfigRouteImport
    }
    '/_dashboard/system/role/$id': {
      id: '/_dashboard/system/role/$id'
      path: '/system/role/$id'
      fullPath: '/system/role/$id'
      preLoaderRoute: typeof DashboardSystemRoleIdImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/trace/issue/$id': {
      id: '/_dashboard/trace/issue/$id'
      path: '/trace/issue/$id'
      fullPath: '/trace/issue/$id'
      preLoaderRoute: typeof DashboardTraceIssueIdImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/component/rich-text-editor/': {
      id: '/_dashboard/component/rich-text-editor/'
      path: '/component/rich-text-editor'
      fullPath: '/component/rich-text-editor'
      preLoaderRoute: typeof DashboardComponentRichTextEditorIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/component/s3-upload/': {
      id: '/_dashboard/component/s3-upload/'
      path: '/component/s3-upload'
      fullPath: '/component/s3-upload'
      preLoaderRoute: typeof DashboardComponentS3UploadIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/export/big-data/': {
      id: '/_dashboard/export/big-data/'
      path: '/export/big-data'
      fullPath: '/export/big-data'
      preLoaderRoute: typeof DashboardExportBigDataIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/payment/recharge/': {
      id: '/_dashboard/payment/recharge/'
      path: '/payment/recharge'
      fullPath: '/payment/recharge'
      preLoaderRoute: typeof DashboardPaymentRechargeIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/payment/subscription/': {
      id: '/_dashboard/payment/subscription/'
      path: '/payment/subscription'
      fullPath: '/payment/subscription'
      preLoaderRoute: typeof DashboardPaymentSubscriptionIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/api/': {
      id: '/_dashboard/system/api/'
      path: '/system/api'
      fullPath: '/system/api'
      preLoaderRoute: typeof DashboardSystemApiIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/job/': {
      id: '/_dashboard/system/job/'
      path: '/system/job'
      fullPath: '/system/job'
      preLoaderRoute: typeof DashboardSystemJobIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/permission/': {
      id: '/_dashboard/system/permission/'
      path: '/system/permission'
      fullPath: '/system/permission'
      preLoaderRoute: typeof DashboardSystemPermissionIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/role/': {
      id: '/_dashboard/system/role/'
      path: '/system/role'
      fullPath: '/system/role'
      preLoaderRoute: typeof DashboardSystemRoleIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/system/user/': {
      id: '/_dashboard/system/user/'
      path: '/system/user'
      fullPath: '/system/user'
      preLoaderRoute: typeof DashboardSystemUserIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/trace/issue/': {
      id: '/_dashboard/trace/issue/'
      path: '/trace/issue'
      fullPath: '/trace/issue'
      preLoaderRoute: typeof DashboardTraceIssueIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/trace/session/': {
      id: '/_dashboard/trace/session/'
      path: '/trace/session'
      fullPath: '/trace/session'
      preLoaderRoute: typeof DashboardTraceSessionIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/user/my-application/': {
      id: '/_dashboard/user/my-application/'
      path: '/user/my-application'
      fullPath: '/user/my-application'
      preLoaderRoute: typeof DashboardUserMyApplicationIndexImport
      parentRoute: typeof DashboardImport
    }
    '/_dashboard/e-commerce/product/category/': {
      id: '/_dashboard/e-commerce/product/category/'
      path: '/e-commerce/product/category'
      fullPath: '/e-commerce/product/category'
      preLoaderRoute: typeof DashboardECommerceProductCategoryIndexImport
      parentRoute: typeof DashboardImport
    }
  }
}

// Create and export the route tree

interface AuthenticationRouteChildren {
  AuthenticationSignInIndexRoute: typeof AuthenticationSignInIndexRoute
  AuthenticationSignUpIndexRoute: typeof AuthenticationSignUpIndexRoute
  AuthenticationOauth2CodeFeishuRoute: typeof AuthenticationOauth2CodeFeishuRoute
}

const AuthenticationRouteChildren: AuthenticationRouteChildren = {
  AuthenticationSignInIndexRoute: AuthenticationSignInIndexRoute,
  AuthenticationSignUpIndexRoute: AuthenticationSignUpIndexRoute,
  AuthenticationOauth2CodeFeishuRoute: AuthenticationOauth2CodeFeishuRoute,
}

const AuthenticationRouteWithChildren = AuthenticationRoute._addFileChildren(
  AuthenticationRouteChildren,
)

interface DashboardSystemConfigRouteRouteChildren {
  DashboardSystemConfigIdRoute: typeof DashboardSystemConfigIdRoute
}

const DashboardSystemConfigRouteRouteChildren: DashboardSystemConfigRouteRouteChildren =
  {
    DashboardSystemConfigIdRoute: DashboardSystemConfigIdRoute,
  }

const DashboardSystemConfigRouteRouteWithChildren =
  DashboardSystemConfigRouteRoute._addFileChildren(
    DashboardSystemConfigRouteRouteChildren,
  )

interface DashboardRouteChildren {
  Dashboard403Route: typeof Dashboard403Route
  DashboardSystemConfigRouteRoute: typeof DashboardSystemConfigRouteRouteWithChildren
  DashboardDashboardIndexRoute: typeof DashboardDashboardIndexRoute
  DashboardFaultIndexRoute: typeof DashboardFaultIndexRoute
  DashboardSystemRoleIdRoute: typeof DashboardSystemRoleIdRoute
  DashboardTraceIssueIdRoute: typeof DashboardTraceIssueIdRoute
  DashboardComponentRichTextEditorIndexRoute: typeof DashboardComponentRichTextEditorIndexRoute
  DashboardComponentS3UploadIndexRoute: typeof DashboardComponentS3UploadIndexRoute
  DashboardExportBigDataIndexRoute: typeof DashboardExportBigDataIndexRoute
  DashboardPaymentRechargeIndexRoute: typeof DashboardPaymentRechargeIndexRoute
  DashboardPaymentSubscriptionIndexRoute: typeof DashboardPaymentSubscriptionIndexRoute
  DashboardSystemApiIndexRoute: typeof DashboardSystemApiIndexRoute
  DashboardSystemJobIndexRoute: typeof DashboardSystemJobIndexRoute
  DashboardSystemPermissionIndexRoute: typeof DashboardSystemPermissionIndexRoute
  DashboardSystemRoleIndexRoute: typeof DashboardSystemRoleIndexRoute
  DashboardSystemUserIndexRoute: typeof DashboardSystemUserIndexRoute
  DashboardTraceIssueIndexRoute: typeof DashboardTraceIssueIndexRoute
  DashboardTraceSessionIndexRoute: typeof DashboardTraceSessionIndexRoute
  DashboardUserMyApplicationIndexRoute: typeof DashboardUserMyApplicationIndexRoute
  DashboardECommerceProductCategoryIndexRoute: typeof DashboardECommerceProductCategoryIndexRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  Dashboard403Route: Dashboard403Route,
  DashboardSystemConfigRouteRoute: DashboardSystemConfigRouteRouteWithChildren,
  DashboardDashboardIndexRoute: DashboardDashboardIndexRoute,
  DashboardFaultIndexRoute: DashboardFaultIndexRoute,
  DashboardSystemRoleIdRoute: DashboardSystemRoleIdRoute,
  DashboardTraceIssueIdRoute: DashboardTraceIssueIdRoute,
  DashboardComponentRichTextEditorIndexRoute:
    DashboardComponentRichTextEditorIndexRoute,
  DashboardComponentS3UploadIndexRoute: DashboardComponentS3UploadIndexRoute,
  DashboardExportBigDataIndexRoute: DashboardExportBigDataIndexRoute,
  DashboardPaymentRechargeIndexRoute: DashboardPaymentRechargeIndexRoute,
  DashboardPaymentSubscriptionIndexRoute:
    DashboardPaymentSubscriptionIndexRoute,
  DashboardSystemApiIndexRoute: DashboardSystemApiIndexRoute,
  DashboardSystemJobIndexRoute: DashboardSystemJobIndexRoute,
  DashboardSystemPermissionIndexRoute: DashboardSystemPermissionIndexRoute,
  DashboardSystemRoleIndexRoute: DashboardSystemRoleIndexRoute,
  DashboardSystemUserIndexRoute: DashboardSystemUserIndexRoute,
  DashboardTraceIssueIndexRoute: DashboardTraceIssueIndexRoute,
  DashboardTraceSessionIndexRoute: DashboardTraceSessionIndexRoute,
  DashboardUserMyApplicationIndexRoute: DashboardUserMyApplicationIndexRoute,
  DashboardECommerceProductCategoryIndexRoute:
    DashboardECommerceProductCategoryIndexRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof DashboardRouteWithChildren
  '/403': typeof Dashboard403Route
  '/system/config': typeof DashboardSystemConfigRouteRouteWithChildren
  '/sign-in': typeof AuthenticationSignInIndexRoute
  '/sign-up': typeof AuthenticationSignUpIndexRoute
  '/dashboard': typeof DashboardDashboardIndexRoute
  '/fault': typeof DashboardFaultIndexRoute
  '/flow/editor': typeof FlowEditorIndexRoute
  '/oauth2/code/feishu': typeof AuthenticationOauth2CodeFeishuRoute
  '/system/config/$id': typeof DashboardSystemConfigIdRoute
  '/system/role/$id': typeof DashboardSystemRoleIdRoute
  '/trace/issue/$id': typeof DashboardTraceIssueIdRoute
  '/component/rich-text-editor': typeof DashboardComponentRichTextEditorIndexRoute
  '/component/s3-upload': typeof DashboardComponentS3UploadIndexRoute
  '/export/big-data': typeof DashboardExportBigDataIndexRoute
  '/payment/recharge': typeof DashboardPaymentRechargeIndexRoute
  '/payment/subscription': typeof DashboardPaymentSubscriptionIndexRoute
  '/system/api': typeof DashboardSystemApiIndexRoute
  '/system/job': typeof DashboardSystemJobIndexRoute
  '/system/permission': typeof DashboardSystemPermissionIndexRoute
  '/system/role': typeof DashboardSystemRoleIndexRoute
  '/system/user': typeof DashboardSystemUserIndexRoute
  '/trace/issue': typeof DashboardTraceIssueIndexRoute
  '/trace/session': typeof DashboardTraceSessionIndexRoute
  '/user/my-application': typeof DashboardUserMyApplicationIndexRoute
  '/e-commerce/product/category': typeof DashboardECommerceProductCategoryIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof DashboardRouteWithChildren
  '/403': typeof Dashboard403Route
  '/system/config': typeof DashboardSystemConfigRouteRouteWithChildren
  '/sign-in': typeof AuthenticationSignInIndexRoute
  '/sign-up': typeof AuthenticationSignUpIndexRoute
  '/dashboard': typeof DashboardDashboardIndexRoute
  '/fault': typeof DashboardFaultIndexRoute
  '/flow/editor': typeof FlowEditorIndexRoute
  '/oauth2/code/feishu': typeof AuthenticationOauth2CodeFeishuRoute
  '/system/config/$id': typeof DashboardSystemConfigIdRoute
  '/system/role/$id': typeof DashboardSystemRoleIdRoute
  '/trace/issue/$id': typeof DashboardTraceIssueIdRoute
  '/component/rich-text-editor': typeof DashboardComponentRichTextEditorIndexRoute
  '/component/s3-upload': typeof DashboardComponentS3UploadIndexRoute
  '/export/big-data': typeof DashboardExportBigDataIndexRoute
  '/payment/recharge': typeof DashboardPaymentRechargeIndexRoute
  '/payment/subscription': typeof DashboardPaymentSubscriptionIndexRoute
  '/system/api': typeof DashboardSystemApiIndexRoute
  '/system/job': typeof DashboardSystemJobIndexRoute
  '/system/permission': typeof DashboardSystemPermissionIndexRoute
  '/system/role': typeof DashboardSystemRoleIndexRoute
  '/system/user': typeof DashboardSystemUserIndexRoute
  '/trace/issue': typeof DashboardTraceIssueIndexRoute
  '/trace/session': typeof DashboardTraceSessionIndexRoute
  '/user/my-application': typeof DashboardUserMyApplicationIndexRoute
  '/e-commerce/product/category': typeof DashboardECommerceProductCategoryIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_authentication': typeof AuthenticationRouteWithChildren
  '/_dashboard': typeof DashboardRouteWithChildren
  '/_dashboard/403': typeof Dashboard403Route
  '/_dashboard/system/config': typeof DashboardSystemConfigRouteRouteWithChildren
  '/_authentication/sign-in/': typeof AuthenticationSignInIndexRoute
  '/_authentication/sign-up/': typeof AuthenticationSignUpIndexRoute
  '/_dashboard/dashboard/': typeof DashboardDashboardIndexRoute
  '/_dashboard/fault/': typeof DashboardFaultIndexRoute
  '/flow_/editor/': typeof FlowEditorIndexRoute
  '/_authentication/oauth2/code/feishu': typeof AuthenticationOauth2CodeFeishuRoute
  '/_dashboard/system/config/$id': typeof DashboardSystemConfigIdRoute
  '/_dashboard/system/role/$id': typeof DashboardSystemRoleIdRoute
  '/_dashboard/trace/issue/$id': typeof DashboardTraceIssueIdRoute
  '/_dashboard/component/rich-text-editor/': typeof DashboardComponentRichTextEditorIndexRoute
  '/_dashboard/component/s3-upload/': typeof DashboardComponentS3UploadIndexRoute
  '/_dashboard/export/big-data/': typeof DashboardExportBigDataIndexRoute
  '/_dashboard/payment/recharge/': typeof DashboardPaymentRechargeIndexRoute
  '/_dashboard/payment/subscription/': typeof DashboardPaymentSubscriptionIndexRoute
  '/_dashboard/system/api/': typeof DashboardSystemApiIndexRoute
  '/_dashboard/system/job/': typeof DashboardSystemJobIndexRoute
  '/_dashboard/system/permission/': typeof DashboardSystemPermissionIndexRoute
  '/_dashboard/system/role/': typeof DashboardSystemRoleIndexRoute
  '/_dashboard/system/user/': typeof DashboardSystemUserIndexRoute
  '/_dashboard/trace/issue/': typeof DashboardTraceIssueIndexRoute
  '/_dashboard/trace/session/': typeof DashboardTraceSessionIndexRoute
  '/_dashboard/user/my-application/': typeof DashboardUserMyApplicationIndexRoute
  '/_dashboard/e-commerce/product/category/': typeof DashboardECommerceProductCategoryIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/403'
    | '/system/config'
    | '/sign-in'
    | '/sign-up'
    | '/dashboard'
    | '/fault'
    | '/flow/editor'
    | '/oauth2/code/feishu'
    | '/system/config/$id'
    | '/system/role/$id'
    | '/trace/issue/$id'
    | '/component/rich-text-editor'
    | '/component/s3-upload'
    | '/export/big-data'
    | '/payment/recharge'
    | '/payment/subscription'
    | '/system/api'
    | '/system/job'
    | '/system/permission'
    | '/system/role'
    | '/system/user'
    | '/trace/issue'
    | '/trace/session'
    | '/user/my-application'
    | '/e-commerce/product/category'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/403'
    | '/system/config'
    | '/sign-in'
    | '/sign-up'
    | '/dashboard'
    | '/fault'
    | '/flow/editor'
    | '/oauth2/code/feishu'
    | '/system/config/$id'
    | '/system/role/$id'
    | '/trace/issue/$id'
    | '/component/rich-text-editor'
    | '/component/s3-upload'
    | '/export/big-data'
    | '/payment/recharge'
    | '/payment/subscription'
    | '/system/api'
    | '/system/job'
    | '/system/permission'
    | '/system/role'
    | '/system/user'
    | '/trace/issue'
    | '/trace/session'
    | '/user/my-application'
    | '/e-commerce/product/category'
  id:
    | '__root__'
    | '/'
    | '/_authentication'
    | '/_dashboard'
    | '/_dashboard/403'
    | '/_dashboard/system/config'
    | '/_authentication/sign-in/'
    | '/_authentication/sign-up/'
    | '/_dashboard/dashboard/'
    | '/_dashboard/fault/'
    | '/flow_/editor/'
    | '/_authentication/oauth2/code/feishu'
    | '/_dashboard/system/config/$id'
    | '/_dashboard/system/role/$id'
    | '/_dashboard/trace/issue/$id'
    | '/_dashboard/component/rich-text-editor/'
    | '/_dashboard/component/s3-upload/'
    | '/_dashboard/export/big-data/'
    | '/_dashboard/payment/recharge/'
    | '/_dashboard/payment/subscription/'
    | '/_dashboard/system/api/'
    | '/_dashboard/system/job/'
    | '/_dashboard/system/permission/'
    | '/_dashboard/system/role/'
    | '/_dashboard/system/user/'
    | '/_dashboard/trace/issue/'
    | '/_dashboard/trace/session/'
    | '/_dashboard/user/my-application/'
    | '/_dashboard/e-commerce/product/category/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthenticationRoute: typeof AuthenticationRouteWithChildren
  DashboardRoute: typeof DashboardRouteWithChildren
  FlowEditorIndexRoute: typeof FlowEditorIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthenticationRoute: AuthenticationRouteWithChildren,
  DashboardRoute: DashboardRouteWithChildren,
  FlowEditorIndexRoute: FlowEditorIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_authentication",
        "/_dashboard",
        "/flow_/editor/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_authentication": {
      "filePath": "_authentication.tsx",
      "children": [
        "/_authentication/sign-in/",
        "/_authentication/sign-up/",
        "/_authentication/oauth2/code/feishu"
      ]
    },
    "/_dashboard": {
      "filePath": "_dashboard.tsx",
      "children": [
        "/_dashboard/403",
        "/_dashboard/system/config",
        "/_dashboard/dashboard/",
        "/_dashboard/fault/",
        "/_dashboard/system/role/$id",
        "/_dashboard/trace/issue/$id",
        "/_dashboard/component/rich-text-editor/",
        "/_dashboard/component/s3-upload/",
        "/_dashboard/export/big-data/",
        "/_dashboard/payment/recharge/",
        "/_dashboard/payment/subscription/",
        "/_dashboard/system/api/",
        "/_dashboard/system/job/",
        "/_dashboard/system/permission/",
        "/_dashboard/system/role/",
        "/_dashboard/system/user/",
        "/_dashboard/trace/issue/",
        "/_dashboard/trace/session/",
        "/_dashboard/user/my-application/",
        "/_dashboard/e-commerce/product/category/"
      ]
    },
    "/_dashboard/403": {
      "filePath": "_dashboard/403.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/config": {
      "filePath": "_dashboard/system/config/route.tsx",
      "parent": "/_dashboard",
      "children": [
        "/_dashboard/system/config/$id"
      ]
    },
    "/_authentication/sign-in/": {
      "filePath": "_authentication/sign-in/index.tsx",
      "parent": "/_authentication"
    },
    "/_authentication/sign-up/": {
      "filePath": "_authentication/sign-up/index.tsx",
      "parent": "/_authentication"
    },
    "/_dashboard/dashboard/": {
      "filePath": "_dashboard/dashboard/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/fault/": {
      "filePath": "_dashboard/fault/index.tsx",
      "parent": "/_dashboard"
    },
    "/flow_/editor/": {
      "filePath": "flow_/editor/index.tsx"
    },
    "/_authentication/oauth2/code/feishu": {
      "filePath": "_authentication/oauth2/code/feishu.tsx",
      "parent": "/_authentication"
    },
    "/_dashboard/system/config/$id": {
      "filePath": "_dashboard/system/config/$id.tsx",
      "parent": "/_dashboard/system/config"
    },
    "/_dashboard/system/role/$id": {
      "filePath": "_dashboard/system/role/$id.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/trace/issue/$id": {
      "filePath": "_dashboard/trace/issue/$id.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/component/rich-text-editor/": {
      "filePath": "_dashboard/component/rich-text-editor/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/component/s3-upload/": {
      "filePath": "_dashboard/component/s3-upload/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/export/big-data/": {
      "filePath": "_dashboard/export/big-data/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/payment/recharge/": {
      "filePath": "_dashboard/payment/recharge/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/payment/subscription/": {
      "filePath": "_dashboard/payment/subscription/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/api/": {
      "filePath": "_dashboard/system/api/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/job/": {
      "filePath": "_dashboard/system/job/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/permission/": {
      "filePath": "_dashboard/system/permission/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/role/": {
      "filePath": "_dashboard/system/role/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/system/user/": {
      "filePath": "_dashboard/system/user/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/trace/issue/": {
      "filePath": "_dashboard/trace/issue/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/trace/session/": {
      "filePath": "_dashboard/trace/session/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/user/my-application/": {
      "filePath": "_dashboard/user/my-application/index.tsx",
      "parent": "/_dashboard"
    },
    "/_dashboard/e-commerce/product/category/": {
      "filePath": "_dashboard/e-commerce/product/category/index.tsx",
      "parent": "/_dashboard"
    }
  }
}
ROUTE_MANIFEST_END */

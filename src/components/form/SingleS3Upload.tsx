import type { ProFormUploadButtonProps } from '@ant-design/pro-components'
import type { UploadFile } from 'antd'
import { api } from '@/api'
import { ProFormUploadButton } from '@ant-design/pro-components'
import { Image } from 'antd'
import { useEffect, useState } from 'react'

type SingleS3UploadProps = Omit<ProFormUploadButtonProps, 'value' | 'onChange'> & {
  value?: string
  onChange?: (value: string | undefined) => void
  bucket: string
}

export function SingleS3Upload({ value, onChange, bucket, ...props }: SingleS3UploadProps) {
  const [fileList, setFileList] = useState<UploadFile<string>[]>([])
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState('')

  useEffect(() => {
    if (value) {
      api.s3service.preSignedUrl({
        bucket,
        method: 'GET',
        objectName: value,
      }).then((url) => {
        setFileList([{
          uid: value,
          name: value,
          status: 'done',
          response: value,
          url,
        }])
      })
    }
    else {
      setFileList([])
    }
  }, [bucket, value])

  return (
    <>
      <ProFormUploadButton
        value={fileList}
        onChange={({ fileList }) => {
          const allDone = fileList.every(file => file.status === 'done')
          if (allDone) {
            // 单文件上传，只取第一个文件
            const firstFile = fileList[0]
            onChange?.(firstFile ? firstFile.response as string : undefined)
          }
          else {
            setFileList(fileList)
          }
        }}
        fieldProps={{
          async customRequest({ file, onProgress, onSuccess }) {
            const method = 'PUT'
            const objectName = crypto.randomUUID().replace(/-/g, '')

            const uploadUrl = await api.s3service.preSignedUrl({
              bucket,
              method,
              objectName,
            })

            const xhr = new XMLHttpRequest()
            xhr.open(method, uploadUrl)
            xhr.upload.addEventListener('progress', (event) => {
              const percent = Math.round((event.loaded / event.total) * 100)
              onProgress?.({ percent })
            })

            xhr.addEventListener('load', () => {
              if (xhr.status === 200) {
                onSuccess?.(objectName)
              }
            })

            xhr.send(file)
          },
          async onRemove(file) {
            const objectName = file.response as string
            const deleteUrl = await api.s3service.preSignedUrl({
              bucket,
              method: 'DELETE',
              objectName,
            })
            await fetch(deleteUrl, {
              method: 'DELETE',
            })
            // 单文件上传，删除后设置为 undefined
            onChange?.(undefined)
            return true
          },
          onPreview(file) {
            setPreviewImage(file.url!)
            setPreviewOpen(true)
          },
          maxCount: 1, // 限制只能上传一个文件
        }}
        {...props}
      />
      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: visible => setPreviewOpen(visible),
            afterOpenChange: visible => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </>
  )
}

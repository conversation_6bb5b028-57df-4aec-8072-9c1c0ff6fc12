@layer theme, base, antd, components, utilities;

@import 'tailwindcss';
@plugin "@tailwindcss/typography";

@theme inline {
  --color-text: var(--ant-color-text);
  --color-icon: var(--ant-color-icon);
  --color-border: var(--ant-color-border);
  --color-border-secondary: var(--ant-color-border-secondary);
  --color-container: var(--ant-color-bg-container);
  --color-layout: var(--ant-color-bg-layout);
  --color-error: var(--ant-color-error);
  --color-error-filled: var(--ant-color-error-bg-filled-hover);
  --color-secondary: var(--ant-color-text-secondary);
  --color-success: var(--ant-color-success);
  --color-primary: var(--ant-color-primary);
  --color-bg-text-hover: var(--ant-color-bg-text-hover);
  --color-icon-hover: var(--ant-color-icon-hover);
}

@layer base {
  * {
    @apply border-border-secondary;
  }
  td {
    @apply tabular-nums;
  }
}

@layer components {
  .card {
    @apply p-4 rounded-md bg-container shadow-sm;
  }
  .ant-image {
    display: block;
  }
  .ant-table {
    @apply font-mono;
  }

  /* Antd styles overrides */
  .ant-select-tree-switcher,
  .ant-tree-switcher {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-tree .ant-tree-switcher, 
  .ant-tree .ant-tree-checkbox {
    margin-inline-end: 0;
  }

  .ant-tree .ant-tree-node-content-wrapper {
    padding-inline: 6px;
  }

  .ant-empty .ant-empty-footer {
    margin-top: 4px;
  }
}

:root {
  interpolate-size: allow-keywords;
}

/* Tiptap */
.tiptap {
  .node-s3-image {
    /* @apply rounded-md; */
    &.ProseMirror-selectednode {
      @apply outline-3 outline-primary;
    }
  }
  p.is-empty::before {
    color: #adb5bd;
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }
}
import type {Cha<PERSON><PERSON>, Comparator} from './';

export interface MediaType {
    ALL: MediaType;
    ALL_VALUE: string;
    APPLICATION_ATOM_XML: MediaType;
    APPLICATION_ATOM_XML_VALUE: string;
    APPLICATION_CBOR: MediaType;
    APPLICATION_CBOR_VALUE: string;
    APPLICATION_FORM_URLENCODED: MediaType;
    APPLICATION_FORM_URLENCODED_VALUE: string;
    APPLICATION_GRAPHQL: MediaType;
    APPLICATION_GRAPHQL_VALUE: string;
    APPLICATION_GRAPHQL_RESPONSE: MediaType;
    APPLICATION_GRAPHQL_RESPONSE_VALUE: string;
    APPLICATION_JSON: MediaType;
    APPLICATION_JSON_VALUE: string;
    APPLICATION_JSON_UTF8: MediaType;
    APPLICATION_JSON_UTF8_VALUE: string;
    APPLICATION_OCTET_STREAM: MediaType;
    APPLICATION_OCTET_STREAM_VALUE: string;
    APPLICATION_PDF: MediaType;
    APPLICATION_PDF_VALUE: string;
    APPLICATION_PROBLEM_JSON: MediaType;
    APPLICATION_PROBLEM_JSON_VALUE: string;
    APPLICATION_PROBLEM_JSON_UTF8: MediaType;
    APPLICATION_PROBLEM_JSON_UTF8_VALUE: string;
    APPLICATION_PROBLEM_XML: MediaType;
    APPLICATION_PROBLEM_XML_VALUE: string;
    APPLICATION_PROTOBUF: MediaType;
    APPLICATION_PROTOBUF_VALUE: string;
    APPLICATION_RSS_XML: MediaType;
    APPLICATION_RSS_XML_VALUE: string;
    APPLICATION_NDJSON: MediaType;
    APPLICATION_NDJSON_VALUE: string;
    APPLICATION_STREAM_JSON: MediaType;
    APPLICATION_STREAM_JSON_VALUE: string;
    APPLICATION_XHTML_XML: MediaType;
    APPLICATION_XHTML_XML_VALUE: string;
    APPLICATION_XML: MediaType;
    APPLICATION_XML_VALUE: string;
    APPLICATION_YAML: MediaType;
    APPLICATION_YAML_VALUE: string;
    IMAGE_GIF: MediaType;
    IMAGE_GIF_VALUE: string;
    IMAGE_JPEG: MediaType;
    IMAGE_JPEG_VALUE: string;
    IMAGE_PNG: MediaType;
    IMAGE_PNG_VALUE: string;
    MULTIPART_FORM_DATA: MediaType;
    MULTIPART_FORM_DATA_VALUE: string;
    MULTIPART_MIXED: MediaType;
    MULTIPART_MIXED_VALUE: string;
    MULTIPART_RELATED: MediaType;
    MULTIPART_RELATED_VALUE: string;
    TEXT_EVENT_STREAM: MediaType;
    TEXT_EVENT_STREAM_VALUE: string;
    TEXT_HTML: MediaType;
    TEXT_HTML_VALUE: string;
    TEXT_MARKDOWN: MediaType;
    TEXT_MARKDOWN_VALUE: string;
    TEXT_PLAIN: MediaType;
    TEXT_PLAIN_VALUE: string;
    TEXT_XML: MediaType;
    TEXT_XML_VALUE: string;
    QUALITY_VALUE_COMPARATOR: Comparator<MediaType>;
    SPECIFICITY_COMPARATOR: Comparator<MediaType>;
    qualityValue: number;
    wildcardType: boolean;
    wildcardSubtype: boolean;
    concrete: boolean;
    type: string;
    subtype: string;
    subtypeSuffix: string;
    charset: Charset;
    parameters: {[key:string]: string};
}

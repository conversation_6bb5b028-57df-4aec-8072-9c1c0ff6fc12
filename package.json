{"name": "kiss-frontend", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "api": "node scripts/generate-api.js", "lint": "eslint --fix"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-query": "^5.69.0", "@tanstack/react-router": "^1.114.25", "@tiptap-pro/extension-file-handler": "^2.18.0-beta.6", "@tiptap-pro/extension-unique-id": "^2.18.0-beta.6", "@tiptap/extension-floating-menu": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@xyflow/react": "^12.6.0", "ahooks": "^3.8.4", "antd": "^5.24.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "js-yaml": "^4.1.0", "monaco-editor": "0.52.2", "radash": "^12.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.14", "ts-pattern": "^5.7.1", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@eslint-react/eslint-plugin": "^1.19.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/router-plugin": "^1.114.25", "@types/js-yaml": "^4.0.9", "@types/node": "^22.13.10", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.3.4", "adm-zip": "^0.5.16", "eslint": "^9.10.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.4", "fs-extra": "^11.3.0", "globals": "^15.15.0", "temp-dir": "^3.0.0", "typescript": "~5.7.2", "user-agent-data-types": "^0.4.2", "uuid": "^11.1.0", "vite": "^6.2.0"}}